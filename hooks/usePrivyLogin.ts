import {
  useAuthorizationSignature,
  useOAuthTokens,
  usePrivy,
  useToken,
} from "@privy-io/react-auth";
import {
  useCreateWallet,
  useSignRawHash,
} from "@privy-io/react-auth/extended-chains";
import { useCallback, useEffect, useState } from "react";
import { createPrivyHeaders } from "@/utils/privyAuth";
import { blake2b } from "@noble/hashes/blake2b";
import { SuiClient, getFullnodeUrl } from "@mysten/sui/client";
import { toHex } from "@mysten/sui/utils";
import { Transaction } from "@mysten/sui/transactions";
import { PublicKey, toSerializedSignature } from "@mysten/sui/cryptography";
import { Ed25519PublicKey } from "@mysten/sui/keypairs/ed25519";
import { useIdentityToken } from "@privy-io/react-auth";

export const usePrivyLogin = () => {
  const { login, authenticated, user, getAccessToken, token } = usePrivy();
  const { createWallet } = useCreateWallet();
  const [suiWallet, setSuiWallet] = useState<any>(null);
  const [isCreatingWallet, setIsCreatingWallet] = useState(false);
  const [isSigningTransaction, setIsSigningTransaction] = useState(false);
  const { generateAuthorizationSignature } = useAuthorizationSignature();

  useEffect(() => {
    if (!authenticated) return;
    const init = async () => {
      const data = await getAccessToken();
      console.log(data, "data");
    };

    init();
  }, [authenticated]);

  // Function to get Google account information
  const getGoogleAccountInfo = useCallback(() => {
    if (!user || !user.linkedAccounts) return null;
    console.log(user, "user");

    const googleAccount = user.linkedAccounts.find(
      (account) => account.type === "google_oauth"
    );

    if (googleAccount) {
      return {
        googleId: googleAccount.subject,
        email: googleAccount.email,
        name: googleAccount.name,
        verifiedAt: googleAccount.verifiedAt,
      };
    }

    return null;
  }, [user]);
  const createSuiWallet = useCallback(async () => {
    if (!authenticated || !user) return;
    const existSuiWallet = user.wallet?.chainType === "sui";

    if (existSuiWallet) {
      console.log("existSuiWallet", user.wallet);
      setSuiWallet(user.wallet);
      return user.wallet;
    }

    try {
      setIsCreatingWallet(true);
      const { user: walletUser, wallet } = await createWallet({
        chainType: "sui",
      });
      setSuiWallet(wallet);

      if (wallet?.address) {
        localStorage.setItem(
          "privy_sui_wallet",
          JSON.stringify({
            address: wallet.address,
            chainType: "sui",
            createdAt: new Date().toISOString(),
          })
        );
      }

      console.log("Sui wallet created:", wallet);
      return wallet;
    } catch (error) {
      console.error("Error creating Sui wallet:", error);
    } finally {
      setIsCreatingWallet(false);
    }
  }, [authenticated, user, createWallet]);

  useEffect(() => {
    const savedWallet = localStorage.getItem("privy_sui_wallet");
    if (savedWallet) {
      try {
        const walletData = JSON.parse(savedWallet);
        setSuiWallet({ address: walletData.address });
      } catch (error) {
        console.error("Error loading saved wallet:", error);
      }
    }
  }, []);

  useEffect(() => {
    if (authenticated && user && !suiWallet) {
      createSuiWallet();
    }
  }, [authenticated, user, suiWallet, createSuiWallet]);

  // Log Google account info when user is authenticated
  useEffect(() => {
    if (authenticated && user) {
      const googleInfo = getGoogleAccountInfo();
      if (googleInfo) {
        console.log("Google Account Info:", googleInfo);
        console.log("Google ID:", googleInfo.googleId);
      }
    }
  }, [authenticated, user, getGoogleAccountInfo]);

  const onPrivyLogin = useCallback(() => {
    if (!authenticated) {
      login();
    }
  }, [login, authenticated]);

  const onPrivyLogout = useCallback(() => {
    // Privy logout logic if needed
    setSuiWallet(null);
    localStorage.removeItem("privy_sui_wallet");
  }, []);

  // Raw signing function for Sui transactions
  const signRawHash = useCallback(
    async (hash: string) => {
      if (!authenticated || !user || !suiWallet) {
        throw new Error("User not authenticated or wallet not available");
      }

      try {
        setIsSigningTransaction(true);

        const walletRequest = {
          version: 1,
          url: `https://api.privy.io/v1/wallets/${suiWallet.id}/raw_sign`,
          method: "POST",
          headers: {
            "privy-app-id": "cmcx1ca72032ek80mm7e52086",
          },
          body: {
            params: {
              hash: `0x${hash}`,
            },
          },
        } as const;

        const authorizationSignature = await generateAuthorizationSignature(
          walletRequest
        );

        const appId =
          process.env.NEXT_PUBLIC_PRIVY_APP_ID || "cmcx1ca72032ek80mm7e52086";
        const appSecret =
          "5HktCvAcb1YNFPpyq3jY8eXXa3ubfEHcsqEpZ3RyTXbBvPSu4EnyGJK2yguk5eAoHQTk9RiG5uVswBxKjwtnag1x";

        const response = await fetch(walletRequest.url, {
          method: walletRequest.method,
          headers: createPrivyHeaders(appId, appSecret, authorizationSignature),
          body: JSON.stringify(walletRequest.body),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            `Privy signing failed: ${response.statusText} - ${
              errorData.error?.message || ""
            }`
          );
        }

        const result = await response.json();

        return result.data;
      } catch (error) {
        console.error("Error signing raw hash:", error);
        throw error;
      } finally {
        setIsSigningTransaction(false);
      }
    },
    [authenticated, user, suiWallet, generateAuthorizationSignature]
  );
  console.log(suiWallet, "suiWallet");
  const test = async () => {
    if (!suiWallet) {
      return;
    }

    const client = new SuiClient({ url: getFullnodeUrl("mainnet") });

    const recipient = ""; // replace with actual address
    const amount = 10000; // in MIST (1 SUI = 10^9 MIST)
    const tx = new Transaction();
    const [coin] = tx.splitCoins(tx.gas, [tx.pure.u64(amount)]);
    tx.transferObjects([coin], tx.pure.address(recipient));
    tx.setSender(suiWallet.address);

    const serializedTx = await tx.build({
      client,
    });

    const hashBytes = blake2b(serializedTx, { dkLen: 32 });
    const hashHex = toHex(hashBytes);

    const data = await signRawHash(hashHex); //

    const txSignature = toSerializedSignature({
      signature: data.signature,
      signatureScheme: "ED25519",
      // publicKey
    });
    const result = await client.executeTransactionBlock({
      transactionBlock: serializedTx,
      signature: txSignature,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });

    console.log(result, "result");
  };

  useEffect(() => {
    if (authenticated && user) {
      test();
    }
  }, [authenticated, user, suiWallet, signRawHash]);

  return {
    onPrivyLogin,
    onPrivyLogout,
    authenticated,
    user,
    suiWallet,
    isCreatingWallet,
    isSigningTransaction,
    createSuiWallet,
    signRawHash,
    getGoogleAccountInfo,
  };
};
