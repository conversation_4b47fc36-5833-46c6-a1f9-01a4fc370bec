import React from 'react';
import { usePrivyLogin } from '@/hooks/usePrivyLogin';

export const GoogleIdExample = () => {
  const { authenticated, user, getGoogleAccountInfo } = usePrivyLogin();

  if (!authenticated || !user) {
    return <div>Please login with <PERSON> first</div>;
  }

  const googleInfo = getGoogleAccountInfo();

  if (!googleInfo) {
    return <div>No Google account linked</div>;
  }

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Google Account Information</h3>
      <div className="space-y-2">
        <div>
          <strong>Google ID:</strong> {googleInfo.googleId}
        </div>
        <div>
          <strong>Email:</strong> {googleInfo.email}
        </div>
        <div>
          <strong>Name:</strong> {googleInfo.name}
        </div>
      </div>
    </div>
  );
};
